<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>华创云联 - Footer 重新设计演示</title>
    <link rel="stylesheet" href="wp-content/themes/hcylsoftcn/assets/css/footer-redesign.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8f9fa;
            line-height: 1.6;
        }

        .demo-header {
            background: linear-gradient(135deg, #4f7fe8 0%, #74b9ff 100%);
            color: white;
            padding: 60px 20px;
            text-align: center;
        }

        .demo-header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 700;
        }

        .demo-header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .demo-content {
            max-width: 1200px;
            margin: 50px auto;
            padding: 0 20px;
        }

        .demo-section {
            background: white;
            border-radius: 12px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }

        .demo-section h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.8rem;
        }

        .demo-section p {
            color: #666;
            margin-bottom: 15px;
            font-size: 1.1rem;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .feature-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #4f7fe8;
        }

        .feature-card h3 {
            color: #4f7fe8;
            margin-bottom: 10px;
        }

        .feature-card p {
            color: #666;
            font-size: 0.95rem;
        }

        .scroll-indicator {
            position: fixed;
            bottom: 30px;
            right: 30px;
            background: #4f7fe8;
            color: white;
            padding: 15px;
            border-radius: 50%;
            cursor: pointer;
            box-shadow: 0 4px 15px rgba(79, 127, 232, 0.3);
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .scroll-indicator:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(79, 127, 232, 0.4);
        }

        @media (max-width: 768px) {
            .demo-header h1 {
                font-size: 2rem;
            }
            
            .demo-header p {
                font-size: 1rem;
            }
            
            .demo-section {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="demo-header">
        <h1>华创云联 Footer 重新设计</h1>
        <p>现代化、响应式的页脚设计演示</p>
    </div>

    <div class="demo-content">
        <div class="demo-section">
            <h2>🎨 设计亮点</h2>
            <p>我们重新设计了网站的页脚部分，采用现代化的设计理念和交互效果，提升用户体验。</p>
            
            <div class="features-grid">
                <div class="feature-card">
                    <h3>🌟 现代化视觉</h3>
                    <p>采用渐变背景、动态装饰元素和现代化的色彩搭配，营造专业而富有活力的视觉效果。</p>
                </div>
                
                <div class="feature-card">
                    <h3>📱 响应式设计</h3>
                    <p>完全响应式布局，在桌面、平板和手机上都能完美显示，确保最佳的用户体验。</p>
                </div>
                
                <div class="feature-card">
                    <h3>✨ 交互动效</h3>
                    <p>丰富的悬停效果、平滑过渡动画和微交互，让页面更加生动有趣。</p>
                </div>
                
                <div class="feature-card">
                    <h3>🏢 品牌展示</h3>
                    <p>新增公司品牌区域，展示企业实力数据，增强品牌形象和可信度。</p>
                </div>
                
                <div class="feature-card">
                    <h3>📞 联系优化</h3>
                    <p>重新设计联系信息布局，使用图标和卡片式设计，信息更清晰易读。</p>
                </div>
                
                <div class="feature-card">
                    <h3>🔗 链接美化</h3>
                    <p>友情链接采用新的视觉设计，增加箭头指示和悬停效果，提升点击体验。</p>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h2>🛠️ 技术特性</h2>
            <p>新的页脚设计采用了多项现代化的前端技术和设计模式：</p>
            
            <div class="features-grid">
                <div class="feature-card">
                    <h3>CSS Grid 布局</h3>
                    <p>使用CSS Grid实现灵活的响应式布局，支持多种屏幕尺寸。</p>
                </div>
                
                <div class="feature-card">
                    <h3>CSS 动画</h3>
                    <p>纯CSS实现的动画效果，包括浮动装饰、悬停过渡等。</p>
                </div>
                
                <div class="feature-card">
                    <h3>渐变背景</h3>
                    <p>多层次渐变背景设计，营造深度和层次感。</p>
                </div>
                
                <div class="feature-card">
                    <h3>移动优先</h3>
                    <p>采用移动优先的设计理念，确保在小屏幕设备上的最佳体验。</p>
                </div>
            </div>
        </div>
    </div>

    <div class="scroll-indicator" onclick="scrollToFooter()">
        ⬇️
    </div>

    <!-- 这里是重新设计的Footer -->
    <div class="footer-main pc-pad">
        <!-- 装饰性背景元素 -->
        <div class="footer-bg-decoration">
            <div class="decoration-circle circle-1"></div>
            <div class="decoration-circle circle-2"></div>
            <div class="decoration-circle circle-3"></div>
        </div>
        
        <div class="footer-container">
            <!-- 公司品牌区域 -->
            <div class="footer-section company-brand">
                <div class="brand-logo">
                    <div style="width: 40px; height: 40px; background: #4f7fe8; border-radius: 8px; margin-right: 12px; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold;">H</div>
                    <h3 class="brand-name">华创云联</h3>
                </div>
                <p class="brand-slogan">专业的软件开发与技术服务提供商</p>
                <div class="brand-stats">
                    <div class="stat-item">
                        <span class="stat-number">10+</span>
                        <span class="stat-label">年经验</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">500+</span>
                        <span class="stat-label">项目案例</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">100+</span>
                        <span class="stat-label">合作伙伴</span>
                    </div>
                </div>
            </div>

            <!-- 联系信息区域 -->
            <div class="footer-section contact-info">
                <h4 class="footer-title">
                    <i class="title-icon">📞</i>
                    联系我们
                </h4>
                <div class="contact-details">
                    <div class="contact-item">
                        <div class="contact-icon-wrapper">
                            <i class="contact-icon phone-icon">📞</i>
                        </div>
                        <div class="contact-content">
                            <span class="contact-label">联系电话</span>
                            <span class="contact-value">************</span>
                        </div>
                    </div>
                    
                    <div class="contact-item">
                        <div class="contact-icon-wrapper">
                            <i class="contact-icon email-icon">✉️</i>
                        </div>
                        <div class="contact-content">
                            <span class="contact-label">企业邮箱</span>
                            <span class="contact-value"><EMAIL></span>
                        </div>
                    </div>
                    
                    <div class="contact-item">
                        <div class="contact-icon-wrapper">
                            <i class="contact-icon location-icon">📍</i>
                        </div>
                        <div class="contact-content">
                            <span class="contact-label">太原公司</span>
                            <span class="contact-value">山西省太原市小店区学府街132号</span>
                        </div>
                    </div>
                    
                    <div class="contact-item">
                        <div class="contact-icon-wrapper">
                            <i class="contact-icon location-icon">📍</i>
                        </div>
                        <div class="contact-content">
                            <span class="contact-label">北京公司</span>
                            <span class="contact-value">北京市朝阳区建国路88号</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 友情链接区域 -->
            <div class="footer-section friendly-links">
                <h4 class="footer-title">
                    <i class="title-icon">🔗</i>
                    友情链接
                </h4>
                <div class="links-grid">
                    <a href="#" target="_blank" rel="noopener" class="friend-link">
                        <span class="link-text">百度</span>
                        <i class="link-arrow">→</i>
                    </a>
                    <a href="#" target="_blank" rel="noopener" class="friend-link">
                        <span class="link-text">腾讯</span>
                        <i class="link-arrow">→</i>
                    </a>
                    <a href="#" target="_blank" rel="noopener" class="friend-link">
                        <span class="link-text">阿里巴巴</span>
                        <i class="link-arrow">→</i>
                    </a>
                    <a href="#" target="_blank" rel="noopener" class="friend-link">
                        <span class="link-text">华为</span>
                        <i class="link-arrow">→</i>
                    </a>
                    <a href="#" target="_blank" rel="noopener" class="friend-link">
                        <span class="link-text">字节跳动</span>
                        <i class="link-arrow">→</i>
                    </a>
                </div>
            </div>

            <!-- 二维码区域 -->
            <div class="footer-section qr-codes">
                <h4 class="footer-title">
                    <i class="title-icon">📱</i>
                    扫码关注
                </h4>
                <div class="qr-container">
                    <div class="qr-item">
                        <div class="qr-img-wrapper">
                            <div style="width: 120px; height: 120px; background: #ddd; border-radius: 8px; display: flex; align-items: center; justify-content: center; color: #666; font-size: 12px; text-align: center;" class="qr-img">企微客服<br>二维码</div>
                            <div class="qr-overlay">
                                <span class="qr-scan-text">扫码咨询</span>
                            </div>
                        </div>
                        <p class="qr-label">企微客服</p>
                    </div>

                    <div class="qr-item">
                        <div class="qr-img-wrapper">
                            <div style="width: 120px; height: 120px; background: #ddd; border-radius: 8px; display: flex; align-items: center; justify-content: center; color: #666; font-size: 12px; text-align: center;" class="qr-img">微信公众号<br>二维码</div>
                            <div class="qr-overlay">
                                <span class="qr-scan-text">扫码关注</span>
                            </div>
                        </div>
                        <p class="qr-label">微信公众号</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 现代化版权信息区域 -->
    <div class="footer-copy pc-pad">
        <div class="copyright-container">
            <div class="copyright-content">
                <div class="copyright-text">
                    <span class="company-name">山西华创云联软件开发股份有限公司</span>
                    <span class="rights-text">保留所有权利</span>
                </div>
                <div class="copyright-links">
                    <a href="https://beian.miit.gov.cn/" target="_blank" class="beian-link">
                        <i class="beian-icon">🔒</i>
                        晋ICP备12345678号
                    </a>
                </div>
            </div>
            <div class="copyright-year">
                © 2024 All Rights Reserved
            </div>
        </div>
    </div>

    <script>
        function scrollToFooter() {
            document.querySelector('.footer-main').scrollIntoView({ 
                behavior: 'smooth' 
            });
        }

        // 添加滚动指示器的动态效果
        window.addEventListener('scroll', function() {
            const scrollIndicator = document.querySelector('.scroll-indicator');
            const footer = document.querySelector('.footer-main');
            const footerTop = footer.offsetTop;
            const scrollTop = window.pageYOffset;
            
            if (scrollTop + window.innerHeight >= footerTop) {
                scrollIndicator.style.opacity = '0.3';
                scrollIndicator.style.pointerEvents = 'none';
            } else {
                scrollIndicator.style.opacity = '1';
                scrollIndicator.style.pointerEvents = 'auto';
            }
        });
    </script>
</body>
</html>
