<?php

/**
 * The dashboard-specific functionality of the plugin.
 *
 * @link       http://example.com
 * @since      1.0.0
 *
 * @package    Hcylsoft
 * @subpackage Hcylsoft/includes
 */

/**
 * The dashboard-specific functionality of the plugin.
 *
 * Defines the plugin name, version, and two examples hooks for how to
 * enqueue the dashboard-specific stylesheet and JavaScript.
 *
 * @package    Hcylsoft
 * @subpackage Hcylsoft/admin
 * <AUTHOR> Name <<EMAIL>>
 */
class Hcylsoft_Admin
{

	/**
	 * The ID of this plugin.
	 *
	 * @since    1.0.0
	 * @access   private
	 * @var      string    $name    The ID of this plugin.
	 */
	private $name;

	/**
	 * The version of this plugin.
	 *
	 * @since    1.0.0
	 * @access   private
	 * @var      string    $version    The current version of this plugin.
	 */
	private $version;

	/**
	 * Initialize the class and set its properties.
	 *
	 * @since    1.0.0
	 * @var      string    $name       The name of this plugin.
	 * @var      string    $version    The version of this plugin.
	 */
	public function __construct($name, $version)
	{

		$this->name = $name;
		$this->version = $version;
	}

	/**
	 * Register the stylesheets for the Dashboard.
	 *
	 * @since    1.0.0
	 */
	public function enqueue_styles()
	{

		/**
		 * This function is provided for demonstration purposes only.
		 *
		 * An instance of this class should be passed to the run() function
		 * defined in Hcylsoft_Admin_Loader as all of the hooks are defined
		 * in that particular class.
		 *
		 * The Hcylsoft_Admin_Loader will then create the relationship
		 * between the defined hooks and the functions defined in this
		 * class.
		 */

		wp_enqueue_style($this->name, plugin_dir_url(__FILE__) . 'css/hcylsoft-admin.css', array(), $this->version, 'all');
		wp_enqueue_style($this->name . 'iconfont', plugin_dir_url(__FILE__) . 'css/iconfont/iconfont.css', array(), $this->version, 'all');
	}


	public function init_menu() {}


	/**
	 * Register the JavaScript for the dashboard.
	 *
	 * @since    1.0.0
	 */
	public function enqueue_scripts()
	{

		/**
		 * This function is provided for demonstration purposes only.
		 *
		 * An instance of this class should be passed to the run() function
		 * defined in Hcylsoft_Admin_Loader as all of the hooks are defined
		 * in that particular class.
		 *
		 * The Hcylsoft_Admin_Loader will then create the relationship
		 * between the defined hooks and the functions defined in this
		 * class.
		 */

		wp_enqueue_script($this->name, plugin_dir_url(__FILE__) . 'js/hcylsoft-admin.js', array('jquery'), $this->version, FALSE);
	}

	public function hcyl_constact_ajax_action_callback()
	{
		include_once plugin_dir_path(__FILE__) . 'class-hcylsoft-constactus-list-table.php';
		Hcyl_Constact_List_Table::add();
	}



	/**
	 * @internal never define functions inside callbacks.
	 * these functions could be run multiple times; this would result in a fatal error.
	 */

	/**
	 * custom option and settings
	 */
	function hcyl_settings_init()
	{
		// Register a new setting for "hcyl" page.
		register_setting('hcyl', 'site_phone');

		// Register a new section in the "hcyl" page.
		add_settings_section(
			'default',
			'',
			'',
			'hcyl'
		);

		add_settings_field(
			'site_phone',
			'联系电话',
			array($this, 'site_phone_settings_callback'),
			'hcyl'
		);
		register_setting('hcyl', 'site_email');
		add_settings_field(
			'site_email',
			'企业邮箱',
			array($this, 'site_email_settings_callback'),
			'hcyl'
		);

		register_setting('hcyl', 'site_address');
		add_settings_field(
			'site_address',
			'太原公司',
			array($this, 'site_address_settings_callback'),
			'hcyl'
		);

		register_setting('hcyl', 'site_address2');
		add_settings_field(
			'site_address2',
			'北京公司',
			array($this, 'site_address_settings_callback_2'),
			'hcyl'
		);

		register_setting('hcyl', 'site_beian');
		add_settings_field(
			'site_beian',
			'网站备案号',
			array($this, 'site_beian_settings_callback'),
			'hcyl'
		);

		register_setting('hcyl', 'site_support_staff');
		add_settings_field(
			'site_support_staff',
			'客服手机号',
			array($this, 'site_support_staff_callback'),
			'hcyl'
		);
		register_setting('hcyl', 'site_support_qrcode');
		add_settings_field(
			'site_support_qrcode',
			'客服企微二维码',
			array($this, 'site_support_qrcode_callback'),
			'hcyl'
		);
		register_setting('hcyl', 'site_wechat_official_qrcode');
		add_settings_field(
			'site_wechat_official_qrcode',
			'企业微信公众号二维码',
			array($this, 'site_wechat_official_qrcode_callback'),
			'hcyl'
		);
		register_setting('hcyl', 'site_support_staff_bot');
		add_settings_field(
			'site_support_staff_bot',
			'企微客户机器人',
			array($this, 'site_support_staff_bot_callback'),
			'hcyl'
		);
		register_setting('hcyl', 'site_friendly_links');
		add_settings_field(
			'site_friendly_links',
			'友情链接',
			array($this, 'site_friendly_links_callback'),
			'hcyl'
		);
	}

	function site_phone_settings_callback()
	{
		$value = get_option('site_phone', '');
		echo '<input style="width: 500px" type="text" name="site_phone" value="' . esc_attr($value) . '" />';
	}

	function site_email_settings_callback()
	{
		$value = get_option('site_email', '');
		echo '<input style="width: 500px" type="text" name="site_email" value="' . esc_attr($value) . '" />';
	}
	function site_address_settings_callback()
	{
		$value = get_option('site_address', '');
		echo '<textarea  style="width: 500px" name="site_address" >' . esc_attr($value) . '</textarea>';
	}
	function site_address_settings_callback_2()
	{
		$value = get_option('site_address2', '');
		echo '<textarea  style="width: 500px" name="site_address2" >' . esc_attr($value) . '</textarea>';
	}
	function site_beian_settings_callback()
	{
		$value = get_option('site_beian', '');
		echo '<textarea style="width: 500px"  name="site_beian" >' . esc_attr($value) . '</textarea>';
	}

	function site_support_qrcode_callback()
	{
		$value = get_option('site_support_qrcode');
?>
		<div id="upload_image_button" class="site_support_qrcode">
			<input type="hidden" name="site_support_qrcode" value="<?php echo esc_attr($value); ?>" />
			<?php
			if ($value == '') {
				echo "<i  class='iconfont icon-shangchuantupian qr_block'></i>";
				echo  "<img id='qrcode' class='qr_none'  />";
			} else {
				echo "<i  class='iconfont icon-shangchuantupian qr_none'></i>";
				echo  "<img id='qrcode' class='qr_block'  src='{$value}' />";
			}
			?>
		</div>
	<?php
	}

	function site_support_staff_callback()
	{
		$value = get_option('site_support_staff', '');
		echo '<input  style="width: 500px" type="tel" name="site_support_staff" value="' . esc_attr($value) . '" />';
	}

	function site_support_staff_bot_callback()
	{
		$value = get_option('site_support_staff_bot', '');
		echo '<textarea style="width: 500px" name="site_support_staff_bot" >' . esc_attr($value) . '</textarea>';
	}

	function site_wechat_official_qrcode_callback()
	{
		$value = get_option('site_wechat_official_qrcode');
	?>
		<div id="upload_wechat_official_button" class="site_wechat_official_qrcode">
			<input type="hidden" name="site_wechat_official_qrcode" value="<?php echo esc_attr($value); ?>" />
			<?php
			if ($value == '') {
				echo "<i  class='iconfont icon-shangchuantupian qr_block'></i>";
				echo  "<img id='wechat_official_qrcode' class='qr_none'  />";
			} else {
				echo "<i  class='iconfont icon-shangchuantupian qr_none'></i>";
				echo  "<img id='wechat_official_qrcode' class='qr_block'  src='{$value}' />";
			}
			?>
		</div>
	<?php
	}

	function site_friendly_links_callback()
	{
		$value = get_option('site_friendly_links', '');
		echo '<textarea style="width: 500px; height: 150px;" name="site_friendly_links" placeholder="请输入友情链接，每行一个，格式：链接名称|链接地址，例如：&#10;百度|https://www.baidu.com&#10;谷歌|https://www.google.com">' . esc_attr($value) . '</textarea>';
		echo '<p style="color: #666; font-size: 12px;">格式说明：每行一个链接，格式为"链接名称|链接地址"，例如：百度|https://www.baidu.com</p>';
	}

	/**
	 * Add the top level menu page.
	 */
	function hcyl_options_page()
	{
		//	$GLOBALS['menu']['25.9'] =  ['',	'read',	'separator'.'25.9', '', 'wp-menu-separator'];
		add_menu_page(
			'网站设置',
			'网站设置',
			'manage_options',
			'hcyl_contactus',
			'',
			'',
			2

		);
		add_submenu_page(
			'hcyl_contactus',
			'客户信息',
			'客户信息',
			'manage_options',
			'hcyl_contactus',
			array($this, 'hcyl_contactus_info')
		);

		add_submenu_page(
			'hcyl_contactus',
			'基本信息',
			'基本信息',
			'manage_options',
			'hcyl_base_setting',
			array($this, 'hcyl_options_page_html')
		);
	}

	function hcyl_contactus_info()
	{
		if (is_file(plugin_dir_path(__FILE__) . 'partials/hcylsoft-constactus-list-table.php')) {
			include_once plugin_dir_path(__FILE__) . 'partials/hcylsoft-constactus-list-table.php';
		}
	}



	/**
	 * Top level menu callback function
	 */
	function hcyl_options_page_html()
	{
		// check user capabilities
		if (! current_user_can('manage_options')) {
			return;
		}
		if (isset($_GET['settings-updated'])) {
			// add settings saved message with the class of "updated"
			add_settings_error('hcyl_messages', 'hcyl_message', '保存成功', 'updated');
		}

		settings_errors('hcyl_messages');
	?>
		<div class="wrap">
			<h1><?php echo esc_html(get_admin_page_title()); ?></h1>
			<form action="options.php" method="post">
				<?php
				// output security fields for the registered setting "hcyl"
				settings_fields('hcyl');
				// output setting sections and their fields
				// (sections are registered for "hcyl", each field is registered to a specific section)
				do_settings_sections('hcyl');
				// output save settings button
				submit_button('保存更改');
				?>
			</form>
		</div>
<?php
	}
}
