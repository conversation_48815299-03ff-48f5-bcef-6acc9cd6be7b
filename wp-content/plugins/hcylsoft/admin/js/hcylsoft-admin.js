(function( $ ) {
	'use strict';


	/**
	 * All of the code for your Dashboard-specific JavaScript source
	 * should reside in this file.
	 *
	 * Note that this assume you're going to use jQuery, so it prepares
	 * the $ function reference to be used within the scope of this
	 * function.
	 *
	 * From here, you're able to define handlers for when the DOM is
	 * ready:
	 *
	 * $(function() {
	 *
	 * });
	 *
	 * Or when the window is loaded:
	 *
	 * $( window ).load(function() {
	 *
	 * });
	 *
	 * ...and so on.
	 *
	 * Remember that ideally, we should not attach any more than a single DOM-ready or window-load handler
	 * for any particular page. Though other scripts in WordPress core, other plugins, and other themes may
	 * be doing this, we should try to minimize doing that in our own work.
	 */

 	$(function (){

		$('#upload_image_button').click(function(e) {
			e.preventDefault();

			// 实例化一个媒体上传器
			var custom_uploader = wp.media({
				title: '选择图片',
				button: {
					text: '选择图片'
				},
				multiple: false  // 是否允许上传多个文件，设置为false表示只能上传一个文件
			});

			// 当用户选择文件后的处理
			custom_uploader.on('select', function() {
				var attachment = custom_uploader.state().get('selection').first().toJSON();
				// 将选择的图片URL放入文本框中或者进行其他处理
				$('#upload_image_button i').css('display','none')
				$('#qrcode').attr('src',attachment.url).css('display','block')
				$('#upload_image_button input[type="hidden"]').val(attachment.url)
			});

			// 打开媒体上传器
			custom_uploader.open();
		});

		// 企业微信公众号二维码上传功能
		$('#upload_wechat_official_button').click(function(e) {
			e.preventDefault();

			// 实例化一个媒体上传器
			var custom_uploader = wp.media({
				title: '选择企业微信公众号二维码图片',
				button: {
					text: '选择图片'
				},
				multiple: false  // 是否允许上传多个文件，设置为false表示只能上传一个文件
			});

			// 当用户选择文件后的处理
			custom_uploader.on('select', function() {
				var attachment = custom_uploader.state().get('selection').first().toJSON();
				// 将选择的图片URL放入文本框中或者进行其他处理
				$('#upload_wechat_official_button i').css('display','none')
				$('#wechat_official_qrcode').attr('src',attachment.url).css('display','block')
				$('#upload_wechat_official_button input[type="hidden"]').val(attachment.url)
			});

			// 打开媒体上传器
			custom_uploader.open();
		});

	})

})( jQuery );
