/* Footer重新设计样式 */
.footer-main {
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  color: #ecf0f1;
  padding: 40px 0 20px;
  margin-top: 50px;
}

.footer-container {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 1fr 1fr 300px;
  gap: 40px;
  padding: 0 20px;
}

/* 公司信息区域 */
.footer-section.company-info {
  min-width: 0;
}

.footer-title {
  color: #4f7fe8;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 2px solid #4f7fe8;
  display: inline-block;
}

.company-details .company-msg {
  margin: 12px 0;
  font-size: 14px;
  line-height: 1.6;
  display: flex;
  align-items: flex-start;
  flex-wrap: nowrap;
}

.contact-icon {
  margin-right: 8px;
  font-size: 16px;
  display: inline-block;
  width: 18px;
  text-align: center;
  flex-shrink: 0;
}

.company-msg span {
  color: #bdc3c7;
  font-weight: 500;
}

/* 友情链接区域 */
.footer-section.friendly-links {
  min-width: 0;
}

.links-grid {
  /* display: grid; */
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 12px 20px;
}

.friend-link {
  color: #bdc3c7;
  text-decoration: none;
  font-size: 14px;
  padding: 4px 8px;
  transition: all 0.3s ease;
  text-align: center;
  /* display: block; */
}

.friend-link:hover {
  color: #4f7fe8;
  text-decoration: underline;
}

/* 二维码区域 */
.footer-section.qr-codes {
  text-align: center;
}

.qr-container {
  display: flex;
  flex-direction: row;
  gap: 20px;
  align-items: center;
  justify-content: center;
}

.qr-item {
  text-align: center;
  padding: 15px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.qr-item:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-3px);
}

.qr-img {
  width: 100px;
  height: 100px;
  object-fit: cover;
  border-radius: 6px;
  border: 2px solid rgba(255, 255, 255, 0.1);
}

.qr-label {
  margin: 10px 0 0 0;
  font-size: 13px;
  color: #bdc3c7;
  font-weight: 500;
}

/* 版权信息 */
.footer-copy {
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  text-align: center;
  padding: 20px 0;
  font-size: 14px;
  color: #bdc3c7;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  margin: 0;
}

.footer-copy a {
  color: #4f7fe8;
  text-decoration: none;
}

.footer-copy a:hover {
  text-decoration: underline;
}

/* 响应式设计 */
@media (max-width: 992px) {
  .footer-container {
    grid-template-columns: 1fr 1fr;
    gap: 30px;
  }

  .footer-section.qr-codes {
    grid-column: 1 / -1;
    margin-top: 20px;
  }

  .qr-container {
    flex-direction: row;
    justify-content: center;
    gap: 25px;
    flex-wrap: wrap;
  }
}

@media (max-width: 768px) {
  .footer-container {
    grid-template-columns: 1fr;
    gap: 25px;
    padding: 0 15px;
  }

  .footer-main {
    padding: 30px 0 15px;
  }

  .links-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 10px 15px;
  }

  .qr-container {
    flex-direction: row;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
  }

  .footer-title {
    font-size: 16px;
  }

  .company-msg {
    font-size: 13px;
  }

  .friend-link {
    font-size: 13px;
    padding: 6px 10px;
  }
}

@media (max-width: 480px) {
  .links-grid {
    grid-template-columns: 1fr;
  }

  .footer-main {
    padding: 25px 0 10px;
  }

  .footer-container {
    padding: 0 10px;
  }
}

/* 移动端隐藏PC版footer */
@media (max-width: 768px) {
  .footer-main.pc-pad {
    display: none;
  }
}

/* 移动端Footer样式 */
.footer-mobile {
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  color: #ecf0f1;
  padding: 20px 0;
  display: none;
}

@media (max-width: 768px) {
  .footer-mobile.mb-only {
    display: block;
  }
}

.mobile-footer-content {
  padding: 0 15px;
  max-width: 100%;
}

.mobile-contact {
  margin-bottom: 20px;
  text-align: center;
}

.mobile-contact h5 {
  color: #4f7fe8;
  font-size: 16px;
  margin-bottom: 10px;
  font-weight: 600;
}

.mobile-contact p {
  margin: 5px 0;
  font-size: 13px;
  color: #bdc3c7;
}

.mobile-qr {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.mobile-qr-item {
  text-align: center;
  background: rgba(255, 255, 255, 0.05);
  padding: 10px;
  border-radius: 6px;
  min-width: 80px;
}

.mobile-qr-item img {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: 4px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.mobile-qr-item span {
  display: block;
  margin-top: 5px;
  font-size: 11px;
  color: #bdc3c7;
}

.mobile-links {
  margin-bottom: 20px;
  text-align: center;
}

.mobile-links h5 {
  color: #4f7fe8;
  font-size: 16px;
  margin-bottom: 10px;
  font-weight: 600;
}

.mobile-links-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
}

.mobile-links-grid a {
  color: #bdc3c7;
  text-decoration: none;
  font-size: 12px;
  padding: 4px 6px;
  transition: all 0.3s ease;
  text-align: center;
}

.mobile-links-grid a:hover {
  color: #4f7fe8;
  text-decoration: underline;
}

.mobile-copyright {
  text-align: center;
  padding-top: 15px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  font-size: 11px;
  color: #7f8c8d;
}

.mobile-copyright p {
  margin: 3px 0;
}

.mobile-copyright a {
  color: #4f7fe8;
  text-decoration: none;
}

.mobile-copyright a:hover {
  text-decoration: underline;
}
