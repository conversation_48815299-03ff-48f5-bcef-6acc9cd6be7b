/* 移动端菜单修复 - 最终解决方案 */

/* 强制隐藏移动端菜单 - 使用最高优先级选择器 */
@media screen and (max-width: 768px) {
  /* 基础隐藏规则 */
  html body .wrapper .header .header-container .header-r ul.nav-menu {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    position: absolute !important;
    top: 100% !important;
    left: 0 !important;
    right: 0 !important;
    background: #fff !important;
    flex-direction: column !important;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1) !important;
    padding: 20px !important;
    gap: 0 !important;
    z-index: 1000 !important;
    border-radius: 0 0 8px 8px !important;
    list-style: none !important;
    margin: 0 !important;
  }

  /* 只有在激活状态下才显示 */
  html body .wrapper .header .header-container .header-r ul.nav-menu.active {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
  }

  /* 确保菜单按钮可见 */
  html body .wrapper .header .header-container .header-r .menu-toggle {
    display: block !important;
    cursor: pointer !important;
  }

  /* 菜单项样式 */
  html body .wrapper .header .header-container .header-r ul.nav-menu.active li {
    border-bottom: 1px solid #eee !important;
    width: 100% !important;
  }

  html body .wrapper .header .header-container .header-r ul.nav-menu.active li:last-child {
    border-bottom: none !important;
  }

  html body .wrapper .header .header-container .header-r ul.nav-menu.active a {
    padding: 15px 0 !important;
    font-size: 16px !important;
    color: #333 !important;
    text-decoration: none !important;
    display: block !important;
  }

  html body .wrapper .header .header-container .header-r ul.nav-menu.active a:hover {
    color: #4f7fe8 !important;
  }

  /* 二级菜单样式 */
  html body .wrapper .header .header-container .header-r ul.nav-menu.active .sub-menu {
    position: static !important;
    opacity: 1 !important;
    visibility: visible !important;
    transform: none !important;
    box-shadow: none !important;
    background: #f8f9fa !important;
    margin: 10px 0 0 0 !important;
    padding: 0 !important;
    border-radius: 0 !important;
    min-width: auto !important;
    display: none !important;
  }

  html body .wrapper .header .header-container .header-r ul.nav-menu.active .submenu-open .sub-menu {
    display: block !important;
  }

  html body .wrapper .header .header-container .header-r ul.nav-menu.active .sub-menu li {
    border-bottom: 1px solid #e9ecef !important;
  }

  html body .wrapper .header .header-container .header-r ul.nav-menu.active .sub-menu a {
    padding: 12px 20px !important;
    font-size: 14px !important;
    color: #666 !important;
  }

  html body .wrapper .header .header-container .header-r ul.nav-menu.active .sub-menu a:hover {
    background: #e9ecef !important;
  }
}

/* 桌面端确保菜单可见 */
@media screen and (min-width: 769px) {
  html body .wrapper .header .header-container .header-r ul.nav-menu {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
    position: static !important;
    background: transparent !important;
    box-shadow: none !important;
    padding: 0 !important;
    gap: 30px !important;
    z-index: auto !important;
    border-radius: 0 !important;
    flex-direction: row !important;
  }

  html body .wrapper .header .header-container .header-r .menu-toggle {
    display: none !important;
  }
}

/* 额外的强制规则 - 防止任何其他样式覆盖 */
@media screen and (max-width: 768px) {
  .nav-menu:not(.active) {
    display: none !important;
  }

  .nav-menu.active {
    display: flex !important;
  }

  /* 更多强制规则 - 尝试不同的选择器 */
  ul.nav-menu:not(.active) {
    display: none !important;
  }

  ul.nav-menu.active {
    display: flex !important;
  }

  /* WordPress生成的菜单类 */
  .menu:not(.active) {
    display: none !important;
  }

  .menu.active {
    display: flex !important;
  }

  /* 通用菜单隐藏 */
  .header-r > ul:not(.active) {
    display: none !important;
  }

  .header-r > ul.active {
    display: flex !important;
  }
}
