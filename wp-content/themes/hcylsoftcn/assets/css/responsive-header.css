/* 响应式头部样式 */
.header {
  background: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  width: 100%;
}

.header-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 70px;
}

.header-l .logo {
  display: flex;
  align-items: center;
}

.header-l .logo img {
  max-height: 50px;
  width: auto;
}

.header-r {
  display: flex;
  align-items: center;
}

.menu-toggle {
  display: none;
  cursor: pointer;
  padding: 10px;
  border-radius: 4px;
  transition: background-color 0.3s ease;
}

.menu-toggle:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.menu-btn {
  transition: transform 0.3s ease;
}

/* 桌面端菜单样式 */
@media (min-width: 769px) {
  .nav-menu {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: 30px;
  }
}

/* 基础菜单样式 */
.nav-menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

.nav-menu li {
  position: relative;
}

.nav-menu a {
  text-decoration: none;
  color: #333;
  font-weight: 500;
  padding: 10px 0;
  transition: color 0.3s ease;
  display: block;
}

.nav-menu a:hover {
  color: #007cba;
}

/* 二级菜单样式 */
.nav-menu .sub-menu {
  position: absolute;
  top: 100%;
  left: 0;
  background: #fff;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-radius: 4px;
  min-width: 200px;
  /* padding: 10px 0; */
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.3s ease;
  z-index: 1001;
  list-style: none;
  margin: 0;
}

.nav-menu li:hover .sub-menu {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.nav-menu .sub-menu li {
  width: 100%;
  border-bottom: 1px solid #f0f0f0;
}

.nav-menu .sub-menu li:last-child {
  border-bottom: none;
}

.nav-menu .sub-menu a {
  padding: 12px 20px;
  font-size: 14px;
  font-weight: 400;
  color: #666;
  white-space: nowrap;
}

.nav-menu .sub-menu a:hover {
  background: #f8f9fa;
  color: #007cba;
}

/* 有子菜单的父级菜单项指示器 */
.nav-menu .menu-item-has-children > a::after {
  content: "▼";
  font-size: 10px;
  margin-left: 8px;
  transition: all 0.3s ease;
  color: #666;
  display: inline-block;
}

.nav-menu .menu-item-has-children:hover > a::after {
  transform: rotate(180deg);
  color: #4f7fe8;
}

/* 平板样式 (768px - 1024px) */
@media (max-width: 1024px) {
  .header-container {
    padding: 0 15px;
  }

  .nav-menu {
    gap: 20px;
  }

  .nav-menu a {
    font-size: 14px;
  }
}

/* 移动端样式 (最大宽度 768px) */
@media (max-width: 768px) {
  .header-container {
    height: 60px;
    padding: 0 15px;
  }

  .menu-toggle {
    display: block;
  }

  .nav-menu {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: #fff;
    flex-direction: column;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 20px;
    gap: 0;
    z-index: 1000;
    border-radius: 0 0 8px 8px;
  }

  .nav-menu.active {
    display: flex !important;
    visibility: visible !important;
    opacity: 1 !important;
  }

  /* 确保菜单正确显示 */
  .nav-menu.active {
    background: #fff !important;
    position: absolute !important;
    top: 100% !important;
    left: 0 !important;
    right: 0 !important;
    z-index: 9999 !important;
    flex-direction: column !important;
    padding: 20px !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
    border-radius: 0 0 8px 8px !important;
  }

  .nav-menu li {
    border-bottom: 1px solid #eee;
    transition: background-color 0.3s ease;
  }

  .nav-menu li:last-child {
    border-bottom: none;
  }

  .nav-menu li:hover {
    background-color: #f8f9fa;
  }

  /* 有子菜单的菜单项激活状态 */
  .nav-menu .menu-item-has-children.submenu-open {
    background-color: #f0f4ff;
  }

  .nav-menu .menu-item-has-children.submenu-open > a {
    color: #4f7fe8;
    font-weight: 600;
  }

  .nav-menu a {
    display: block;
    padding: 15px 0;
    font-size: 16px;
    color: #333;
    transition: color 0.3s ease;
  }

  .nav-menu a:hover {
    color: #4f7fe8;
    text-decoration: none;
  }

  /* 移动端二级菜单样式 */
  .nav-menu .sub-menu {
    position: static;
    opacity: 1;
    visibility: visible;
    transform: none;
    box-shadow: none;
    background: #f8f9fa;
    margin: 10px 0 0 0;
    padding: 0;
    border-radius: 0;
    min-width: auto;
    transition: all 0.3s ease;
    overflow: hidden;
  }

  .nav-menu .sub-menu li {
    border-bottom: 1px solid #e9ecef;
    margin: 0;
  }

  .nav-menu .sub-menu a {
    padding: 12px 20px;
    font-size: 14px;
    color: #666;
  }

  .nav-menu .sub-menu a:hover {
    background: #e9ecef;
  }

  /* 移动端菜单指示器调整 - 重写桌面端样式 */
  .nav-menu .menu-item-has-children > a {
    position: relative;
    padding-right: 30px;
  }

  /* 移动端箭头默认状态 - 覆盖桌面端样式 */
  .nav-menu .menu-item-has-children > a::after {
    content: "▼" !important;
    position: absolute !important;
    right: 0 !important;
    top: 50% !important;
    transform: translateY(-50%) rotate(0deg) !important;
    font-size: 12px !important;
    color: #666 !important;
    transition: all 0.3s ease !important;
    width: 20px !important;
    text-align: center !important;
    margin-left: 0 !important;
    display: block !important;
  }

  /* 移动端箭头展开状态 */
  .nav-menu .menu-item-has-children.submenu-open > a::after {
    transform: translateY(-50%) rotate(180deg) !important;
    color: #4f7fe8 !important;
  }

  /* 移动端禁用hover效果，避免与点击冲突 */
  .nav-menu .menu-item-has-children > a:hover::after {
    transform: translateY(-50%) rotate(0deg) !important;
    color: #666 !important;
  }

  /* 移动端展开状态的hover效果 */
  .nav-menu .menu-item-has-children.submenu-open > a:hover::after {
    transform: translateY(-50%) rotate(180deg) !important;
    color: #4f7fe8 !important;
  }

  /* 移动端子菜单初始隐藏 */
  .nav-menu .sub-menu {
    display: none;
  }

  .nav-menu .submenu-open .sub-menu {
    display: block !important;
  }

  .header-l .logo img {
    max-height: 40px;
  }
}

/* 小屏幕移动端 (最大宽度 480px) */
@media (max-width: 480px) {
  .header-container {
    padding: 0 10px;
  }

  .header-l .logo img {
    max-height: 35px;
  }

  .nav-menu {
    padding: 15px;
  }

  .nav-menu a {
    padding: 12px 0;
    font-size: 15px;
  }
}

/* 为页面内容添加顶部间距，避免被固定头部遮挡 */
body {
  padding-top: 70px;
}

@media (max-width: 768px) {
  body {
    padding-top: 60px;
  }
  .new-icon {
    left: 30px;
  }
}

.new-icon {
  position: absolute;
  display: inline;
  top: 2px;
  right: -20px;
}
