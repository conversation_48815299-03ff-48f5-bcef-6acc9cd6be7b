.about-bg-img {
  position: relative;
}

.wrapper-pad {
  /* padding-top: 90px; */
}

.about-text {
  width: 210px;
  margin: 0 auto;
  position: absolute;
  top: 30%;
  left: 50%;
  margin-left: -105px;
}

.about-c {
  font-size: 46px;
  font-weight: bold;
  line-height: 81px;
  color: #fff;
  letter-spacing: 5px;
}

.about-e {
  font-size: 20px;
  line-height: 24px;
  color: #fff;
  text-align: center;
}

.about-tabs {
  position: absolute;
  bottom: 0px;
  left: 50%;
  margin-left: -605px;
  width: 1210px;
  text-align: center;
}

.computer-img-container-pc {
  text-align: center;
}

.about-tabs li a {
  width: 350px;
  background: #fff;
  display: inline-block;
  line-height: 58px;
  /* padding-left: 85px; */
  /* padding-right: 85px; */
  color: #333;
  font-size: 18px;
}

.selected {
  background: #4f7fe8 !important;
  color: #fff !important;
}

.about-content-main {
  display: none;
}

.dis-block {
  display: block;
}

.dis-none {
  display: none;
}

.mag-t0 {
  margin-top: 0 !important;
}

/*车辆管理系统*/
.product-box {
  padding-top: 60px;
  padding-bottom: 60px;
  overflow: hidden;
}

.product-box-bg {
  background: #F7F9FC;
}

.pro-box-1 {
  background: url("../img/solution/item01/item4-bg.png");
  background-size: 100% 100%;
  background-repeat: no-repeat;
}

.pro-name {
  overflow: hidden;
  width: 1210px;
  padding: 10px 0px;
  margin: 0 auto;
  margin-top: 30px;
}

.jiagou-area-item {
  overflow: hidden;
  margin: 0 auto;
  width: 1170px;
  background: #FFFFFF;
  box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.11);
  border-radius: 10px;
}

.product-title {
  text-align: center;
  font-size: 32px;
  color: #333;
  line-height: 32px;
}

.product-intro {
  font-size: 16px;
  color: #999;
  text-align: center;
  padding-top: 20px;
  padding-bottom: 30px;
}

.product-line {
  width: 56px;
  height: 2px;
  background: #4F7FE8;
  margin: 0 auto;
}
.swiper-container-jiazhimubiao {
  margin-top: 15px;
}
.pro-content {
  padding-top: 135px;
  padding-left: 60px;
  box-sizing: border-box;
}

.application-scenarios img {
  margin: 0 auto;
}

.application-scenarios-discribe {
  margin-top: 19px;
}

.application-scenarios-discribe p {
  font-size: 16px;
  font-family: Microsoft YaHei;
  font-weight: 400;
  color: #333333;
  line-height: 32px;
}

.pro-content p {
  font-size: 16px;
  color: #333;
  line-height: 34px;
  text-align: justify;
}

.pro-box-2 {
  background: #F7F9FC;
}

.pro-intro {
  font-size: 24px;
  color: #333;
  padding-top: 10px;
  padding-bottom: 10px;
  line-height: 32px;
}

.pro-line {
  width: 42px;
  height: 4px;
  background: linear-gradient(90deg, #4F7FE8, #97B5F7);
}

.pro-num {
  /* margin-top: 50px; */
}

.tabs {
  display: inline-block;
  height: 640px;
  background: #FFFFFF;
  box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.11);
  border-radius: 10px 10px 10px 10px;
}

.tabs-ul {
  position: relative;
  width: 100%;
  height: 70px;
  border-radius: 3px 0px 0px 0px;
  border-bottom: 3px solid #D9DDE8;
  display: flex;
}

.tabs-ul div {
  height: 100%;
  border-radius: 3px 0px 0px 0px;
  text-align: center;
  float: left;
  flex: auto;
  line-height: 70px;
  background: #ffffff;
  color: #737373;
  font-size: 16px;
  cursor: pointer;
  font-family: Microsoft YaHei;
  font-weight: 400;
}

/* .tabs-ul div:hover {
  background: #FF8A8A;
  color: #ffffff;
} */

.tabs-ul .active {
  color: #4F7FE8;
  border-bottom: 3px solid #4F7FE8;
}

.tabs-ul div:last-child {
  border-right: 1px solid #f7f7f7;
}

.jiagou-title {
  font-size: 24px;
  color: #4F7FE8;
  padding-top: 10px;
  padding-bottom: 10px;
  line-height: 32px;
}

.jiagou-discribe {}

.jiagou-item {
  width: 1170px;
  display: flex;
  margin: 0 auto;
  align-items: center;
  justify-content: center;
}

.jiagou-item div {
  display: inline-block;
  text-align: center;
  flex: auto;
  padding: 30px 0;
  cursor: pointer;
}

.jiagou-item .active {
  border-bottom: 3px solid #4F7FE8;
}

.jiagou-container {
  background: #FFFFFF;
  box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.11);
  border-radius: 10px;
  padding: 25px;
}

.jiagou-content {
  font-size: 14px;
  line-height: 32px;
  text-align: justify;
}

.img-padding {
  padding: 30px;
}

.img-padding img {
  height: 350px;
}

.tabPane {
  width: 100%;
  text-align: left;
  font-size: 14px;
  padding-bottom: 30px;
}

.tabPane div {
  box-sizing: border-box;
  padding: 20px;
  text-align: center;
}

.pro-num-r {
  padding-left: 80px;
}

.pro-car-img .pro-detail {
  padding-left: 80px;
  margin-top: 20px;
}

.pro-car-box .pro-detail {
  padding-right: 50px;
  margin-top: 20px;
}

.pro-detail p {
  line-height: 32px;
  font-size: 14px;
  color: #666;
  text-align: justify;
}

.pro-detail li {
  line-height: 32px;
  font-size: 14px;
  color: #666;
}

.pro-detail li span {
  margin-left: 10px;
}

.pro-detail-item {
  margin-top: 20px;
  display: -webkit-flex;
  /* Safari */
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pro-detail-item span {
  font-size: 16px;
  color: #333;
  margin-left: 0px !important;
}

.pro-detail-item img {
  vertical-align: middle;
  margin-top: -5px;
}

.btn-container {
  margin-bottom: 20px;
  text-align: center;
}

.btn-container .btn {
  display: inline-block;
  width: 130px;
  height: 44px;
  margin-right: 15px;
  text-align: center;
  font-size: 14px;
  font-family: Microsoft YaHei;
  font-weight: 400;
  color: #666666;
  border: 1px solid #E3E7F7;
  line-height: 44px;
  background: #FFFFFF;
  /* box-shadow: 0px 4px 10px 0px rgba(41, 91, 197, 0.39); */
  border-radius: 3px;
}

.value-target-container {
  display: flex;
  justify-content: space-between;
  /* padding: 30px 300px; */
}

.value-target-container .value-target-item {
  width: 240px;
  height: 210px;
  display: inline-block;
  background: #fff;
  text-align: center;
  position: relative;
  padding: 5px;
}

.value-target-item .top-img {
  margin-top: 30px;
  height: 50px;
}

.value-target-item-title {
  font-family: Source Han Sans CN;
  font-weight: 500;
  color: #333333;
  font-size: 16px;
  margin-top: 15px;
}

.value-target-item-discribe {
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #999999;
  margin-top: 10px;
  padding: 0 40px;
}

.value-target-item-jiantou {
  position: absolute;
  background: #e6edfc;
  padding: 5px 0;
  bottom: 0px;
  left: 0;
  right: 0;
}

.value-target-detail {
  background: #fff;
  padding: 35px;
  overflow: hidden;
  height: 522px;
}

.value-target-detail .value-target-logo {
  position: relative;
}

.value-target-detail .logo-title {
  position: absolute;
  font-size: 26px;
  font-family: Source Han Sans CN;
  font-weight: 500;
  color: #333333;
  top: 55px;
  left: 45px;
}

.value-target-detail .value-target-text {
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #666666;
  line-height: 32px;
  margin-top: 40px;
}

.value-target-detail .value-target-list {
  font-family: Source Han Sans CN;
  font-weight: 400;
  color: #666666;
  line-height: 32px;
}
.value-target-detail {
  height: auto;
}
.value-target-detail2 {
  display: none;
}
.value-target-list .num {
  color: #4F7FE8;
}

.value-target-list .small-title {
  margin-right: 40px;
}

.value-target-container .active {
  border-top: 3px solid #4F7FE8;
  box-shadow: 0px 0px 5px 0px rgba(0, 0, 0, 0.09);
}

.btn-container .active {
  color: #fff;
  background: #4F7FE8;
  border: 1px solid #4F7FE8;
  box-shadow: 0px 4px 10px 0px rgba(41, 91, 197, 0.39);
}

.bottom-line-container {
  text-align: center;
}

.bottom-line-container .btn-line {
  width: 51px;
  height: 3px;
  display: inline-block;
  margin-right: 15px;
  background: #C0C0C0;
  cursor: pointer;
}

.bottom-line-container .active {
  background: #4F7FE8;
}

/* 机构 */
.bottom-line-container-jigou {
  text-align: center;
}

.bottom-line-container-jigou .btn-line {
  width: 51px;
  height: 3px;
  display: inline-block;
  margin-right: 15px;
  background: #C0C0C0;
  cursor: pointer;
}

.bottom-line-container-jigou .active {
  background: #4F7FE8;
}

/* 合同 */
.bottom-line-container-hetong {
  text-align: center;
}

.bottom-line-container-hetong .btn-line {
  width: 51px;
  height: 3px;
  display: inline-block;
  margin-right: 15px;
  background: #C0C0C0;
  cursor: pointer;
}

.bottom-line-container-hetong .active {
  background: #4F7FE8;
}

/* 进度 */
.bottom-line-container-jindu {
  text-align: center;
}

.bottom-line-container-jindu .btn-line {
  width: 51px;
  height: 3px;
  display: inline-block;
  margin-right: 15px;
  background: #C0C0C0;
  cursor: pointer;
}

.bottom-line-container-jindu .active {
  background: #4F7FE8;
}

/* 投资 */
.bottom-line-container-touzi {
  text-align: center;
}

.bottom-line-container-touzi .btn-line {
  width: 51px;
  height: 3px;
  display: inline-block;
  margin-right: 15px;
  background: #C0C0C0;
  cursor: pointer;
}

.bottom-line-container-touzi .active {
  background: #4F7FE8;
}

/* 规制 */
.bottom-line-container-guizhi {
  text-align: center;
}

.bottom-line-container-guizhi .btn-line {
  width: 51px;
  height: 3px;
  display: inline-block;
  margin-right: 15px;
  background: #C0C0C0;
  cursor: pointer;
}

.bottom-line-container-guizhi .active {
  background: #4F7FE8;
}

/* 督办 */
.bottom-line-container-duban {
  text-align: center;
}

.bottom-line-container-duban .btn-line {
  width: 51px;
  height: 3px;
  display: inline-block;
  margin-right: 15px;
  background: #C0C0C0;
  cursor: pointer;
}

.bottom-line-container-duban .active {
  background: #4F7FE8;
}


.pro-pad {
  margin-top: 0;
}

.pro-box-pc {
  padding-top: 10px;
  padding-bottom: 10px;
}

.pro-pc {
  margin-top: 50px;
}

.huiyiduban-box {
  margin-bottom: 50px;
}

.duban-discribe {
  background: #FFFFFF;
  border: 1px solid #F5F5F5;
  padding: 25px;
  line-height: 32px;
  font-size: 14px;
  color: #666;
  cursor: pointer;
  text-align: justify;
  box-sizing: border-box;
}

.dubanguanli-img-container {
  padding: 20px;
}

.hydb-block {
  width: 100%;
  overflow: hidden;
  margin-bottom: 15px;
}

.duban-item1 .active {
  border-left: 3px solid #4F7FE8;
  box-shadow: 0px 0px 5px 0px rgba(0, 0, 0, 0.09);
}

/* .duban-item2 .active {
  border-left: 3px solid #4F7FE8;
  box-shadow: 0px 0px 5px 0px rgba(0, 0, 0, 0.09);
} */
.dubanguanli-img-pane {
  display: none;
}

.pro-pc-t30 {
  margin-top: 30px;
}

.pro-circle img {
  width: 10px;
  height: 10px;
}

.pro-7-1 {
  width: 16px;
}

.pro-7-2 {
  width: 17px;
}

.pro-7-3 {
  width: 17px;
}

.pro-7-4 {
  width: 22px;
}

/*印章管理系统*/
.seal-content {
  padding-top: 100px;
}

.seal-point-box {
  padding: 17px;
  background: #fff;
  position: relative;
  border: 1px solid #E5EBF4;
  min-height: 183px;
  margin-left: 11px;
  margin-right: 11px;
  margin-bottom: 22px;
}

.seal-point-img {
  width: 95px;
  height: 95px;
}

.seal-point-name {
  font-size: 18px;
  color: #333;
  line-height: 32px;
  padding-left: 15px;
}

.seal-point-content {
  position: relative;
  padding-left: 27px;
  line-height: 26px;
  font-size: 14px;
  color: #999;
}

.seal-point-content::before {
  display: block;
  content: "";
  position: absolute;
  width: 4px;
  height: 4px;
  background: #4F7FE8;
  border-radius: 50%;
  left: 15px;
  top: 11px;
}

.seal-point-order {
  width: 0;
  height: 0;
  border-top: 65px solid #F5F9FE;
  border-left: 68px solid transparent;
  position: absolute;
  top: 0;
  right: 0;
}

.seal-point-num {
  color: #4F7FE8;
  position: absolute;
  top: 10px;
  right: 10px;
}

.seal-resolve-name {
  padding-left: 0;
}

.seal-resolve-content {
  padding-left: 12px;
}

.seal-resolve-content::before {
  left: 0px;
  top: 10px;
}

.seal-solve-box {
  margin-bottom: 30px;
  margin-top: 20px;
}

.solve-img {
  display: block;
  margin: 0 auto;
}

.seal-pc {
  margin-top: 70px;
}

.seal-box .pro-num-r {
  padding-left: 30px;
  margin-top: 0;
}

.seal-box .pro-car-img .pro-detail {
  padding-left: 35px;
}

.seal-box .pro-car-box .pro-detail {
  padding-right: 35px;
}

.seal-box .pro-num {
  margin-top: 0;
}

.seal-pc-1 {
  margin-top: 145px;
}

.pro-value {
  background: url("../img/product/seal/seal-value-bg.png");
  background-size: 100% 100%;
  background-repeat: no-repeat;
}

.seal-value-box {
  width: 18%;
  box-shadow: 0px 0px 8px 0px rgba(212, 212, 212, 0.43);
  border-radius: 10px;
  background: url("../img/product/seal/pro-value-bg.png");
  background-repeat: no-repeat;
  float: left;
  padding: 30px 20px;
  box-sizing: border-box;
  height: 186px;
  margin-left: 10px;
  margin-right: 10px;
  margin-bottom: 15px;
}

.seal-value {
  /*display: flex;
  justify-content: space-between;
  align-items: center;*/
}

.pro-value-img {
  margin: 0 auto;
  display: block;
  margin-top: 25px;
  height: 43px;
}

.pro-value-content {
  margin-top: 22px;
  text-align: center;
  line-height: 30px;
  font-size: 15px;
}

.seal-blank {
  position: relative;
}

.seal-blank img {
  /*display:block;*/
  margin: 0 auto;
}

.seal-role {
  position: absolute;
}

.seal-role1 {
  right: 160px;
  top: 115px;
  max-width: 220px;
  text-align: left;
}

.seal-role2 {
  left: 38px;
  bottom: 85px;
  max-width: 195px;
  text-align: right;
}

.seal-role3 {
  right: 0px;
  bottom: 115px;
  max-width: 220px;
  text-align: left;
}

.role-name {
  font-size: 22px;
  color: #333;
  line-height: 32px;
}

.role-content {
  font-size: 15px;
  color: #999;
  line-height: 24px;
  margin-top: 20px;
}

.seal-value-center {
  position: relative;
  height: 1.03rem;
}

.seal-value-cbox {
  position: absolute;
  left: 50%;
  top: 0;
  transform: translateX(-50%);
}

.achieve-container {
  display: flex;
  justify-content: space-between;
}

.achieve-item-container {
  text-align: center;
}

.achieve-item2-container {
  text-align: center;
  padding: 20px;
}

.achieve-item2-container .top-img img {
  width: 70px;
  height: 88px;
}

.achieve-item2-container .bt-img {
  margin-top: 15px;
}

.achieve-item2-container .bt-img img {
  height: 35px;
}

.value-target-item2 {
  width: 230px;
  height: 210px;
  background: #FFFFFF;
  box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.09);
  margin: 0 15px;
  text-align: center;
  position: relative;
}
.value-target-item2 .top-img {
  width: 100px;
  margin-top: 20px;
}
.value-target-item2 .value-target-item-discribe {
  margin-bottom: 5px;
}
.achieve-item2-container .text {
  font-family: Microsoft YaHei;
  font-weight: 400;
  color: #333333;
}