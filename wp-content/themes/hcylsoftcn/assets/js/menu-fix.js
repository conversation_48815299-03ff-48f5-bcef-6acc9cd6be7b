/**
 * 导航菜单修复脚本
 * 确保导航菜单在所有情况下都能正常显示和工作
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Menu fix script loaded');
    
    // 检查菜单元素是否存在
    const navMenu = document.querySelector('.nav-menu');
    const menuToggle = document.querySelector('.menu-toggle');
    const menuBtn = document.querySelector('.menu-btn');
    
    if (!navMenu) {
        console.error('Nav menu not found! Creating fallback menu...');
        createFallbackMenu();
        return;
    }
    
    // 确保菜单可见
    ensureMenuVisibility();
    
    // 设置移动端菜单功能
    if (menuToggle && navMenu) {
        setupMobileMenu();
    }
    
    // 设置二级菜单功能
    setupSubMenus();
    
    // 窗口大小改变时的处理
    window.addEventListener('resize', handleWindowResize);
});

/**
 * 确保菜单可见性 - 只在桌面端强制显示
 */
function ensureMenuVisibility() {
    const navMenu = document.querySelector('.nav-menu');
    if (navMenu) {
        // 只在桌面端强制设置可见性
        if (window.innerWidth > 768) {
            navMenu.style.display = 'flex';
            navMenu.style.visibility = 'visible';
            navMenu.style.opacity = '1';
        } else {
            // 移动端确保菜单默认隐藏
            if (!navMenu.classList.contains('active')) {
                navMenu.style.display = 'none';
            }
        }

        console.log('Menu visibility ensured for', window.innerWidth > 768 ? 'desktop' : 'mobile');
    }
}

/**
 * 创建备用菜单
 */
function createFallbackMenu() {
    const headerR = document.querySelector('.header-r');
    if (headerR) {
        const fallbackMenu = document.createElement('ul');
        fallbackMenu.className = 'nav-menu fallback-menu';
        fallbackMenu.innerHTML = `
            <li><a href="${window.location.origin}">首页</a></li>
            <li><a href="#" onclick="alert('请在WordPress后台设置菜单')">菜单未设置</a></li>
        `;
        
        // 插入到菜单切换按钮之后
        const menuToggle = headerR.querySelector('.menu-toggle');
        if (menuToggle) {
            menuToggle.insertAdjacentElement('afterend', fallbackMenu);
        } else {
            headerR.appendChild(fallbackMenu);
        }
        
        console.log('Fallback menu created');
    }
}

/**
 * 设置移动端菜单功能
 */
function setupMobileMenu() {
    const menuToggle = document.querySelector('.menu-toggle');
    const navMenu = document.querySelector('.nav-menu');
    const menuBtn = document.querySelector('.menu-btn');
    
    // 移除之前的事件监听器
    menuToggle.removeEventListener('click', handleMenuToggle);
    
    // 添加新的事件监听器
    menuToggle.addEventListener('click', handleMenuToggle);
    
    // 点击页面其他地方关闭菜单
    document.addEventListener('click', function(event) {
        if (!menuToggle.contains(event.target) && !navMenu.contains(event.target)) {
            closeMenu();
        }
    });
    
    console.log('Mobile menu setup complete');
}

/**
 * 处理菜单切换
 */
function handleMenuToggle(event) {
    event.preventDefault();
    event.stopPropagation();
    
    const navMenu = document.querySelector('.nav-menu');
    const menuBtn = document.querySelector('.menu-btn');
    
    if (navMenu.classList.contains('active')) {
        closeMenu();
    } else {
        openMenu();
    }
}

/**
 * 打开菜单
 */
function openMenu() {
    const navMenu = document.querySelector('.nav-menu');
    const menuBtn = document.querySelector('.menu-btn');
    
    navMenu.classList.add('active');
    navMenu.style.display = 'flex';
    
    if (menuBtn) {
        menuBtn.style.transform = 'rotate(90deg)';
    }
    
    console.log('Menu opened');
}

/**
 * 关闭菜单
 */
function closeMenu() {
    const navMenu = document.querySelector('.nav-menu');
    const menuBtn = document.querySelector('.menu-btn');
    
    navMenu.classList.remove('active');
    
    if (window.innerWidth <= 768) {
        navMenu.style.display = 'none';
    }
    
    if (menuBtn) {
        menuBtn.style.transform = 'rotate(0deg)';
    }
    
    // 关闭所有子菜单
    const subMenus = document.querySelectorAll('.sub-menu');
    subMenus.forEach(function(subMenu) {
        subMenu.style.display = 'none';
        const parentItem = subMenu.closest('.menu-item-has-children');
        if (parentItem) {
            parentItem.classList.remove('submenu-open');
        }
    });
    
    console.log('Menu closed');
}

/**
 * 设置二级菜单功能
 */
function setupSubMenus() {
    const parentMenuItems = document.querySelectorAll('.menu-item-has-children');
    
    parentMenuItems.forEach(function(parentItem) {
        const parentLink = parentItem.querySelector('a');
        const subMenu = parentItem.querySelector('.sub-menu');
        
        if (parentLink && subMenu) {
            // 移动端点击展开/收起
            parentLink.addEventListener('click', function(e) {
                if (window.innerWidth <= 768) {
                    e.preventDefault();
                    
                    const isVisible = parentItem.classList.contains('submenu-open');
                    
                    // 先关闭其他子菜单
                    parentMenuItems.forEach(function(item) {
                        if (item !== parentItem) {
                            const otherSubMenu = item.querySelector('.sub-menu');
                            if (otherSubMenu) {
                                otherSubMenu.style.display = 'none';
                                item.classList.remove('submenu-open');
                            }
                        }
                    });
                    
                    // 切换当前子菜单
                    if (isVisible) {
                        subMenu.style.display = 'none';
                        parentItem.classList.remove('submenu-open');
                    } else {
                        subMenu.style.display = 'block';
                        parentItem.classList.add('submenu-open');
                    }
                }
            });
        }
    });
    
    console.log('Sub-menus setup complete');
}

/**
 * 处理窗口大小改变
 */
function handleWindowResize() {
    const navMenu = document.querySelector('.nav-menu');
    const menuBtn = document.querySelector('.menu-btn');
    
    if (window.innerWidth > 768) {
        // 桌面端
        navMenu.classList.remove('active');
        navMenu.style.display = 'flex';
        
        if (menuBtn) {
            menuBtn.style.transform = 'rotate(0deg)';
        }
        
        // 重置所有子菜单状态
        const parentMenuItems = document.querySelectorAll('.menu-item-has-children');
        parentMenuItems.forEach(function(parentItem) {
            const subMenu = parentItem.querySelector('.sub-menu');
            if (subMenu) {
                subMenu.style.display = '';
                parentItem.classList.remove('submenu-open');
            }
        });
    } else {
        // 移动端
        if (!navMenu.classList.contains('active')) {
            navMenu.style.display = 'none';
        }
    }
    
    console.log('Window resized, menu adjusted');
}

// 调试函数
function debugMenu() {
    console.log('=== Menu Debug Info ===');
    console.log('Nav menu:', document.querySelector('.nav-menu'));
    console.log('Menu toggle:', document.querySelector('.menu-toggle'));
    console.log('Menu items:', document.querySelectorAll('.nav-menu li'));
    console.log('Window width:', window.innerWidth);
    console.log('======================');
}

// 在控制台中可以调用 debugMenu() 来调试
window.debugMenu = debugMenu;
