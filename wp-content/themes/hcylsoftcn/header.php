<?php

/**
 * The header for our theme
 *
 * This is the template that displays all of the <head> section and everything up until <div id="content">
 *
 * @link https://developer.wordpress.org/themes/basics/template-files/#template-partials
 *
 * @package hcylsoftcn
 */

?>
<!doctype html>
<html <?php language_attributes(); ?>>

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <link rel="icon" href="<?= get_stylesheet_directory_uri() ?>/assets/favicon.ico">
    <meta name="viewport"
        content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <!-- 百度统计代码 -->
    <script>
        var _hmt = _hmt || [];
        (function() {
            var hm = document.createElement("script");
            hm.src = "https://hm.baidu.com/hm.js?cd0241f3faf5209fc4356e082c3e4dbb";
            var s = document.getElementsByTagName("script")[0];
            s.parentNode.insertBefore(hm, s);
        })();
    </script>
    <?php wp_head(); ?>
</head>

<body>
    <?php wp_body_open(); ?>
    <div class="wrapper">
        <!-- 响应式头部开始 -->
        <div class="header">
            <div class="header-container">
                <div class="header-l">
                    <div class="logo">
                        <?php
                        the_custom_logo();
                        ?>
                    </div>
                </div>
                <div class="header-r">
                    <div class="menu-toggle">
                        <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/app/common/menu.png" width="20" height="20" alt="" class="menu-btn" />
                    </div>
                    <?php
                    // 检查是否有菜单分配到 'menu' 位置
                    if (has_nav_menu('menu')) {
                        wp_nav_menu(array(
                            'theme_location' => 'menu',
                            'menu_class' => 'nav-menu',
                            'container' => false,
                            'depth' => 0,
                            'walker' => new Hcyl_Walker_Nav_Menu()
                        ));
                    } else {
                        // 如果没有分配菜单，显示默认菜单
                        echo '<ul class="nav-menu">';
                        echo '<li><a href="' . home_url() . '">首页</a></li>';
                        echo '<li><a href="' . admin_url('nav-menus.php') . '">设置菜单</a></li>';
                        echo '</ul>';
                    }
                    ?>
                </div>
            </div>
        </div><!-- 响应式头部结束 -->

        <!-- 移动端菜单脚本 -->
        <script>
            // 标记内联脚本已处理菜单
            window.mobileMenuInitialized = true;

            document.addEventListener('DOMContentLoaded', function() {
                const menuToggle = document.querySelector('.menu-toggle');
                const navMenu = document.querySelector('.nav-menu');
                const menuBtn = document.querySelector('.menu-btn');

                if (menuToggle && navMenu) {
                    // 菜单切换功能
                    menuToggle.addEventListener('click', function(e) {
                        e.preventDefault();
                        e.stopPropagation();

                        // 切换菜单显示状态
                        if (navMenu.classList.contains('active')) {
                            navMenu.classList.remove('active');
                            navMenu.style.display = 'none';
                        } else {
                            navMenu.classList.add('active');
                            navMenu.style.display = 'flex';
                        }

                        // 菜单按钮旋转效果
                        if (menuBtn) {
                            menuBtn.style.transform = navMenu.classList.contains('active') ? 'rotate(90deg)' : 'rotate(0deg)';
                        }
                    });

                    // 处理二级菜单的展开/收起
                    const parentMenuItems = navMenu.querySelectorAll('.menu-item-has-children');
                    parentMenuItems.forEach(function(parentItem) {
                        const parentLink = parentItem.querySelector('a');
                        const subMenu = parentItem.querySelector('.sub-menu');

                        if (parentLink && subMenu) {
                            parentLink.addEventListener('click', function(e) {
                                // 只在移动端处理二级菜单
                                if (window.innerWidth <= 768) {
                                    e.preventDefault();

                                    // 检查当前子菜单是否已显示
                                    const isVisible = parentItem.classList.contains('submenu-open');

                                    // 先隐藏所有其他子菜单
                                    parentMenuItems.forEach(function(item) {
                                        if (item !== parentItem) {
                                            const otherSubMenu = item.querySelector('.sub-menu');
                                            if (otherSubMenu) {
                                                otherSubMenu.style.display = 'none';
                                                item.classList.remove('submenu-open');
                                            }
                                        }
                                    });

                                    // 切换当前子菜单
                                    if (isVisible) {
                                        subMenu.style.display = 'none';
                                        parentItem.classList.remove('submenu-open');
                                        console.log('折叠菜单，移除submenu-open类');
                                    } else {
                                        subMenu.style.display = 'block';
                                        parentItem.classList.add('submenu-open');
                                        console.log('展开菜单，添加submenu-open类');
                                    }

                                    // 强制重新渲染以确保CSS生效
                                    setTimeout(function() {
                                        parentItem.offsetHeight; // 触发重排
                                    }, 10);
                                }
                            });
                        }
                    });

                    // 点击页面其他地方关闭菜单
                    document.addEventListener('click', function(event) {
                        if (!menuToggle.contains(event.target) && !navMenu.contains(event.target)) {
                            navMenu.classList.remove('active');
                            navMenu.style.display = 'none';
                            if (menuBtn) {
                                menuBtn.style.transform = 'rotate(0deg)';
                            }

                            // 同时关闭所有子菜单
                            parentMenuItems.forEach(function(parentItem) {
                                const subMenu = parentItem.querySelector('.sub-menu');
                                if (subMenu) {
                                    subMenu.style.display = 'none';
                                    parentItem.classList.remove('submenu-open');
                                }
                            });
                        }
                    });

                    // 窗口大小改变时处理菜单状态
                    window.addEventListener('resize', function() {
                        if (window.innerWidth > 768) {
                            navMenu.classList.remove('active');
                            navMenu.style.display = '';
                            if (menuBtn) {
                                menuBtn.style.transform = 'rotate(0deg)';
                            }

                            // 重置所有子菜单状态
                            parentMenuItems.forEach(function(parentItem) {
                                const subMenu = parentItem.querySelector('.sub-menu');
                                if (subMenu) {
                                    subMenu.style.display = '';
                                    parentItem.classList.remove('submenu-open');
                                }
                            });
                        }
                    });
                }
            });
        </script>