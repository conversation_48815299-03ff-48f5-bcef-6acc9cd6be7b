<?php

/**
 * hcylsoftcn Theme Customizer
 *
 * @package hcylsoftcn
 */

/**
 * Add postMessage support for site title and description for the Theme Customizer.
 *
 * @param WP_Customize_Manager $wp_customize Theme Customizer object.
 */
function hcylsoftcn_customize_register($wp_customize)
{

	$wp_customize->get_setting('blogname')->transport         = 'postMessage';
	$wp_customize->get_setting('blogdescription')->transport  = 'postMessage';
	$wp_customize->get_setting('header_textcolor')->transport = 'postMessage';


	if (isset($wp_customize->selective_refresh)) {
		$wp_customize->selective_refresh->add_partial(
			'blogname',
			array(
				'selector'        => '.site-title a',
				'render_callback' => 'hcylsoftcn_customize_partial_blogname',
			)
		);
		$wp_customize->selective_refresh->add_partial(
			'blogdescription',
			array(
				'selector'        => '.site-description',
				'render_callback' => 'hcylsoftcn_customize_partial_blogdescription',
			)
		);
	}
	$sections =  $wp_customize->sections();
	foreach ($sections as $section) {
		//移除chuang zhi
		$wp_customize->remove_section('colors');
		$wp_customize->remove_section('header_image');
		$wp_customize->remove_section('static_front_page');
	}



	//首页轮播
	$wp_customize->add_panel('hcylsoft', array(
		'title' => '网站设置'
	));

	setting_banners($wp_customize);

	companyProfile($wp_customize);

	aboutUsCompanyProfile($wp_customize);

	//设置video
	setting_bipm_video($wp_customize);
}
add_action('customize_register', 'hcylsoftcn_customize_register');

// 首页banner
function setting_banners($wp_customize)
{


	$wp_customize->add_section('hcylsoft_banner', array(
		'title' => '首页轮播',
		'panel' => 'hcylsoft'
	));



	foreach (banners() as $banner) {

		$wp_customize->add_setting($banner['id'], array(
			'type' => 'theme_mod', // or 'option'
			'capability' => 'edit_theme_options',
			'theme_supports' => '', // Rarely needed.
			'default' => '',
			'transport' => 'refresh', // or postMessage
			'sanitize_callback' => '',
			'sanitize_js_callback' => '', // Basically to_json.
		));

		$wp_customize->add_control(new WP_Customize_Image_Control($wp_customize, $banner['id'], array(
			'label' => $banner['title'],
			'section' => 'hcylsoft_banner',
			'mime_type' => 'image',
		)));
	}
}

//首页公司简介
function companyProfile($wp_customize)
{

	$wp_customize->add_section('hcylsoft_company_profile', array(
		'title' => '首页公司简介',
		'panel' => 'hcylsoft'
	));

	$wp_customize->add_setting('companyprofile', array(
		'type' => 'theme_mod', // or 'option'
		'capability' => 'edit_theme_options',
		'theme_supports' => '', // Rarely needed.
		'default' => '',
		'transport' => 'postMessage', // or postMessage
		'sanitize_callback' => '',
		'sanitize_js_callback' => '', // Basically to_json.
	));

	$wp_customize->add_control('companyprofile', array(
		'type' => 'textarea',
		'label' => '公司简介',
		'input_attrs' => array(
			'placeholder' => '请输入公司简介'
		),
		'section' => 'hcylsoft_company_profile',
	));

	$wp_customize->add_setting('companyprofile_url', array(
		'type' => 'theme_mod', // or 'option'
		'capability' => 'edit_theme_options',
		'theme_supports' => '', // Rarely needed.
		'default' => '',
		'transport' => 'postMessage', // or postMessage
		'sanitize_callback' => '',
		'sanitize_js_callback' => '', // Basically to_json.
	));

	//	  $wp_customize->add_control('companyprofile_url',array(
	//		'type' => 'input',
	//		'label' => '公司简介链接',
	//		'input_attrs' => array(
	//			'placeholder' => '请输入公司链接',
	//			'style' => 'width:100%'
	//		),
	//		'section' => 'hcylsoft_company_profile',
	//	  ));

	$wp_customize->add_setting('companyprofile_img', array(
		'type' => 'theme_mod', // or 'option'
		'capability' => 'edit_theme_options',
		'theme_supports' => '', // Rarely needed.
		'default' => '',
		'transport' => 'refresh', // or postMessage
		'sanitize_callback' => '',
		'sanitize_js_callback' => '', // Basically to_json.
	));

	$wp_customize->add_control(new WP_Customize_Image_Control($wp_customize, 'companyprofile_img', array(
		'label' => '公司简介图片',
		'section' => 'hcylsoft_company_profile',
		'mime_type' => 'image',
	)));

	$wp_customize->selective_refresh->add_partial(
		'companyprofile',
		array(
			'selector'        => '.company-profile-content',
			'render_callback' => function () {
				return get_theme_mod('companyprofile');
			},
		)
	);

	//	$wp_customize->selective_refresh->add_partial(
	//		'companyprofile_url',
	//		array(
	//			'selector'        => '.company-profile-l a',
	//			'render_callback' => function() {
	//				return get_theme_mod('companyprofile_url');
	//			},
	//		)
	//	);

	$wp_customize->selective_refresh->add_partial(
		'companyprofile_img',
		array(
			'selector'        => '.syjj',
			'render_callback' => function () {
				return get_theme_mod('companyprofile_img');
			},
		)
	);
}

//关于我们页面公司简介
function aboutUsCompanyProfile($wp_customize)
{

	$wp_customize->add_section('hcylsoft_aboutus_company_intro', array(
		'title' => '关于我们-公司简介',
		'panel' => 'hcylsoft'
	));

	$wp_customize->add_setting('aboutus_company_intro', array(
		'type' => 'theme_mod',
		'capability' => 'edit_theme_options',
		'theme_supports' => '',
		'default' => '',
		'transport' => 'postMessage',
		'sanitize_callback' => '',
		'sanitize_js_callback' => '',
	));

	$wp_customize->add_control('aboutus_company_intro', array(
		'type' => 'textarea',
		'label' => '关于我们页面公司简介',
		'input_attrs' => array(
			'placeholder' => '请输入关于我们页面的公司简介内容'
		),
		'section' => 'hcylsoft_aboutus_company_intro',
	));

	$wp_customize->selective_refresh->add_partial(
		'aboutus_company_intro',
		array(
			'selector'        => '.company-intro-content',
			'render_callback' => function () {
				return get_theme_mod('aboutus_company_intro');
			},
		)
	);
}

//设置视频
function  setting_bipm_video($wp_customize)
{



	$wp_customize->add_section('hcylsoft_bipm_setting', array(
		'title' => '建智管配置',
		'panel' => 'hcylsoft'
	));
	$wp_customize->add_setting('bipmvideo', array(
		'type' => 'theme_mod', // or 'option'
		'capability' => 'edit_theme_options',
		'theme_supports' => '', // Rarely needed.
		'default' => '',
		'transport' => 'refresh', // or postMessage
		'sanitize_callback' => '',
		'sanitize_js_callback' => '', // Basically to_json.
	));
	$wp_customize->add_control(new WP_Customize_Media_Control($wp_customize, 'bipmvideo', array(
		'label' => '宣传视频',
		'section' => 'hcylsoft_bipm_setting',
		'mime_type' => 'video',
	)));

	$wp_customize->selective_refresh->add_partial(
		'bipmvideo',
		array(
			'selector'        => '.title-center-swiper-right',
		)
	);
}


/**
 * Render the site title for the selective refresh partial.
 *
 * @return void
 */
function hcylsoftcn_customize_partial_blogname()
{
	bloginfo('name');
}

/**
 * Render the site tagline for the selective refresh partial.
 *
 * @return void
 */
function hcylsoftcn_customize_partial_blogdescription()
{
	bloginfo('description');
}

/**
 * Binds JS handlers to make Theme Customizer preview reload changes asynchronously.
 */
function hcylsoftcn_customize_preview_js()
{
	wp_enqueue_script('hcylsoftcn-customizer', get_template_directory_uri() . '/js/customizer.js', array('customize-preview'), _S_VERSION, true);
}
add_action('customize_preview_init', 'hcylsoftcn_customize_preview_js');
