<?php
/**
 * The template for displaying all pages
 *
 * This is the template that displays all pages by default.
 * Please note that this is the WordPress construct of pages
 * and that other 'pages' on your WordPress site may use a
 * different template.
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package hcylsoftcn
 */

get_header();
?>
	<main id="primary">
		<?php
		while ( have_posts() ) :
			the_post();
            $template_value = get_post_meta( get_the_ID(), 'alias', true );
			get_template_part( 'template-parts/content', $template_value );
		endwhile; // End of the loop.
		?>

	</main><!-- #main -->

<?php
get_footer();
