<!-- 重新设计的现代化Footer布局 -->
<div class="footer-main pc-pad">
    <!-- 装饰性背景元素 -->
    <div class="footer-bg-decoration">
        <div class="decoration-circle circle-1"></div>
        <div class="decoration-circle circle-2"></div>
        <div class="decoration-circle circle-3"></div>
    </div>

    <div class="footer-container">
        <!-- 公司品牌区域 -->
        <div class="footer-section company-brand">
            <div class="brand-logo">
                <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/common/logo.png" alt="华创云联" class="footer-logo-img" />
                <h3 class="brand-name">华创云联</h3>
            </div>
            <p class="brand-slogan">专业的软件开发与技术服务提供商</p>
            <div class="brand-stats">
                <div class="stat-item">
                    <span class="stat-number">15+</span>
                    <span class="stat-label">年经验</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">1000+</span>
                    <span class="stat-label">项目案例</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">100+</span>
                    <span class="stat-label">合作伙伴</span>
                </div>
            </div>
        </div>

        <!-- 联系信息区域 -->
        <div class="footer-section contact-info">
            <h4 class="footer-title">
                <i class="title-icon">📞</i>
                联系我们
            </h4>
            <div class="contact-details">
                <div class="contact-item">
                    <div class="contact-icon-wrapper">
                        <i class="contact-icon phone-icon">📞</i>
                    </div>
                    <div class="contact-content">
                        <span class="contact-label">联系电话</span>
                        <span class="contact-value"><?= get_option('site_phone'); ?></span>
                    </div>
                </div>

                <div class="contact-item">
                    <div class="contact-icon-wrapper">
                        <i class="contact-icon email-icon">✉️</i>
                    </div>
                    <div class="contact-content">
                        <span class="contact-label">企业邮箱</span>
                        <span class="contact-value"><?= get_option('site_email'); ?></span>
                    </div>
                </div>

                <div class="contact-item">
                    <div class="contact-icon-wrapper">
                        <i class="contact-icon location-icon">📍</i>
                    </div>
                    <div class="contact-content">
                        <span class="contact-label">太原公司</span>
                        <span class="contact-value"><?= get_option('site_address'); ?></span>
                    </div>
                </div>

                <?php if (get_option('site_address2')): ?>
                    <div class="contact-item">
                        <div class="contact-icon-wrapper">
                            <i class="contact-icon location-icon">📍</i>
                        </div>
                        <div class="contact-content">
                            <span class="contact-label">北京公司</span>
                            <span class="contact-value"><?= get_option('site_address2'); ?></span>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- 二维码区域 -->
        <div class="footer-section qr-codes">
            <h4 class="footer-title">
                <i class="title-icon">📱</i>
                扫码关注
            </h4>
            <div class="qr-container">
                <!-- 企微客服二维码 -->
                <?php if (get_option('site_support_qrcode')): ?>
                    <div class="qr-item">
                        <div class="qr-img-wrapper">
                            <img src="<?= get_option('site_support_qrcode'); ?>" alt="企微客服" class="qr-img" />
                            <div class="qr-overlay">
                                <span class="qr-scan-text">扫码咨询</span>
                            </div>
                        </div>
                        <p class="qr-label">企微客服</p>
                    </div>
                <?php endif; ?>

                <!-- 企业微信公众号二维码 -->
                <?php
                $wechat_official_qrcode = get_option('site_wechat_official_qrcode');
                if (!empty($wechat_official_qrcode)) : ?>
                    <div class="qr-item">
                        <div class="qr-img-wrapper">
                            <img src="<?= $wechat_official_qrcode ?>" alt="微信公众号" class="qr-img" />
                            <div class="qr-overlay">
                                <span class="qr-scan-text">扫码关注</span>
                            </div>
                        </div>
                        <p class="qr-label">微信公众号</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- 友情链接区域 -->
        <?php
        $friendly_links = get_option('site_friendly_links', '');
        if (!empty($friendly_links)) :
            $links = explode("\n", trim($friendly_links));
        ?>
            <div class="footer-section friendly-links">
                <h4 class="footer-title">
                    <i class="title-icon">🔗</i>
                    友情链接
                </h4>
                <div class="links-grid">
                    <?php foreach ($links as $link) :
                        $link = trim($link);
                        if (!empty($link) && strpos($link, '|') !== false) :
                            list($name, $url) = explode('|', $link, 2);
                            $name = trim($name);
                            $url = trim($url);
                            if (!empty($name) && !empty($url)) :
                    ?>
                                <a href="<?= esc_url($url) ?>" target="_blank" rel="noopener" class="friend-link">
                                    <span class="link-text"><?= esc_html($name) ?></span>
                                    <i class="link-arrow">→</i>
                                </a>
                    <?php
                            endif;
                        endif;
                    endforeach; ?>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>
<!-- 现代化版权信息区域 -->
<div class="footer-copy pc-pad">
    <div class="copyright-container">
        <div class="copyright-content">
            <div class="copyright-text">
                <span class="company-name">山西华创云联软件开发股份有限公司</span>
                <span class="rights-text">保留所有权利</span>
            </div>
            <div class="copyright-links">
                <a href="https://beian.miit.gov.cn/" target="_blank" class="beian-link">
                    <?= get_option('site_beian'); ?>
                </a>
            </div>
        </div>
        <div class="copyright-year">
            © <?= date('Y'); ?> All Rights Reserved
        </div>
    </div>
</div><!-- pc尾部结束 -->
<!-- 移动端尾部开始 -->
<div class="footer-mobile mb-only">
    <div class="mobile-footer-content">
        <!-- 移动端联系信息 -->
        <div class="mobile-contact">
            <h5>联系我们</h5>
            <p>电话：<?= get_option('site_phone'); ?></p>
            <p>邮箱：<?= get_option('site_email'); ?></p>
        </div>

        <!-- 移动端二维码 -->
        <div class="mobile-qr">
            <?php if (get_option('site_support_qrcode')): ?>
                <div class="mobile-qr-item">
                    <img src="<?= get_option('site_support_qrcode'); ?>" alt="企微客服" />
                    <span>企微客服</span>
                </div>
            <?php endif; ?>

            <?php
            $wechat_official_qrcode = get_option('site_wechat_official_qrcode');
            if (!empty($wechat_official_qrcode)) : ?>
                <div class="mobile-qr-item">
                    <img src="<?= $wechat_official_qrcode ?>" alt="微信公众号" />
                    <span>微信公众号</span>
                </div>
            <?php endif; ?>
        </div>

        <!-- 移动端友情链接 -->
        <?php
        $friendly_links = get_option('site_friendly_links', '');
        if (!empty($friendly_links)) :
            $links = explode("\n", trim($friendly_links));
            $mobile_links = array_slice($links, 0, 6); // 移动端只显示前6个链接
        ?>
            <div class="mobile-links">
                <h5>友情链接</h5>
                <div class="mobile-links-grid">
                    <?php foreach ($mobile_links as $link) :
                        $link = trim($link);
                        if (!empty($link) && strpos($link, '|') !== false) :
                            list($name, $url) = explode('|', $link, 2);
                            $name = trim($name);
                            $url = trim($url);
                            if (!empty($name) && !empty($url)) :
                    ?>
                                <a href="<?= esc_url($url) ?>" target="_blank" rel="noopener"><?= esc_html($name) ?></a>
                    <?php
                            endif;
                        endif;
                    endforeach; ?>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <!-- 移动端版权信息 -->
    <div class="mobile-copyright">
        <p>山西华创云联软件开发股份有限公司 保留所有权利</p>
        <p><a href="https://beian.miit.gov.cn/"><?= get_option('site_beian'); ?></a></p>
    </div>
</div><!-- 移动端尾部结束 -->
<div class="right-fix-box pc-pad">
    <ul>
        <li>
            <div class="right-a">
                <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/common/wechat.png" alt="" class="right-img" style="cursor: pointer;" />
                <p>官方企微</p>
                <img src="<?php
                            $qrcode = get_option('site_support_qrcode');
                            echo $qrcode;
                            ?>" alt=""
                    class="right-hover" style="max-width: 150px;" />
            </div>
        </li>
        <?php
        $wechat_official_qrcode = get_option('site_wechat_official_qrcode');
        if (!empty($wechat_official_qrcode)) : ?>
            <li>
                <div class="right-a">
                    <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/common/wechat.png" alt="" class="right-img" style="cursor: pointer;" />
                    <p>微信公众号</p>
                    <img src="<?= $wechat_official_qrcode ?>" alt="" class="right-hover" style="max-width: 150px;" />
                </div>
            </li>
        <?php endif; ?>
        <li>
            <div class="right-gotop">
                <img src="<?= get_stylesheet_directory_uri() ?>/assets/img/common/gotop.png" alt="" class="right-img" />
                <p>回到顶部</p>
            </div>
        </li>
    </ul>
</div>